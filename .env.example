# Admin wallet address (authorized to create land titles)
VITE_ADMIN_ADDRESS=

# Smart contract application ID (set after deployment)
VITE_APP_ID=

# Algorand Node URLs (TestNet)
VITE_ALGOD_NODE_URL=https://testnet-api.algonode.cloud
VITE_INDEXER_URL=https://testnet-idx.algonode.cloud

# Nodely IPFS Configuration
VITE_IPFS_GATEWAY_URL=https://ipfs.nodely.dev/ipfs/
VITE_NODELY_API_URL=https://api.nodely.dev/v1
VITE_NODELY_API_KEY=
VITE_NODELY_PROJECT_ID=