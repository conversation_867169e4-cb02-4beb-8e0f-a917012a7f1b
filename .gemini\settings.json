{"mcp": {"inputs": [{"type": "promptString", "id": "pg_url", "description": "PostgreSQL URL (e.g. postgresql://user:pass@localhost:5432/mydb)"}], "servers": {}}, "mcpServers": {"github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "*********************************************************************************************"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "disabled": false}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "disabled": false}, "time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone", "Africa/Nairobi"], "disabled": false}, "memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "disabled": false}, "brave-search": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-brave-search"], "env": {"BRAVE_API_KEY": "BSAdINau4faxPNy47uflnmInSuWm2Nk"}, "disabled": false}, "mcp-docs": {"type": "sse", "serverUrl": "https://gitmcp.io/docs"}, "github.com/supabase-community/supabase-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}, "@21st-dev/magic": {"command": "cmd", "args": ["/c", "npx", "-y", "@21st-dev/magic@latest", "API_KEY=\"1d045a1a4c0957fec21fb05412a3fe2e1d1f8fb31c00968e0d41841fdd14e296\""]}, "google-maps": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-google-maps"], "env": {"GOOGLE_MAPS_API_KEY": "AIzaSyD3Au2pbjXpdYutD539iMnRwq-N61F4DLM"}}, "figma-dev-mode-mcp-server": {"command": "npx", "args": ["mcp-remote", "http://127.0.0.1:3845/sse"], "env": {}}}}