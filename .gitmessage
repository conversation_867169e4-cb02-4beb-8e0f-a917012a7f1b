# Commit message guidelines
# Format: <type>(<scope>): <subject>
#
# <body>
#
# <footer>

# Types:
# feat:     A new feature
# fix:      A bug fix
# docs:     Documentation changes
# style:    Formatting, missing semi-colons, etc.
# refactor: Code refactoring
# test:     Adding tests
# chore:    Build process or tool changes
#
# Example:
# feat(Home): Add hero background image with UI improvements
# 
# - Used ardhi.jpg as full-page background
# - Added overlay for text contrast
# - Improved typography and spacing
#
# Fixes #123
